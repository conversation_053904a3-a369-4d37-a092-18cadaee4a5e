# VS Code Sawron Monorepo

A comprehensive code quality analysis system consisting of a VS Code extension for keystroke tracking and a web application for AI-powered analysis using Google Gemini. Features include user authentication, Git project management, Figma integration, and development progress tracking.

## 🏗️ Architecture

This is a **Yarn Berry-based Turborepo monorepo** with Docker development environment support and the following packages:

- **`packages/vscode-extension`** - VS Code extension for keystroke tracking
- **`packages/web-app`** - Next.js 14+ web application with AI analysis
- **`packages/shared`** - Shared TypeScript types and utilities

## 🐳 Docker Development Environment (Recommended)

### Prerequisites

- Docker and Docker Compose installed
- Node.js 18+ (for local development)

### Quick Start with Docker

1. **Clone and setup:**

   ```bash
   git clone <repository-url>
   cd vscode-keylogger
   ```

2. **Start development environment:**

   ```bash
   yarn docker:dev
   ```

3. **Install dependencies and start web app:**

   ```bash
   yarn install
   yarn workspace web-app dev
   ```

4. **Access services:**
   - Web App: http://localhost:3000
   - PostgreSQL: localhost:5433
   - Redis: localhost:6380
   - pgAdmin: http://localhost:5051 (<EMAIL> / dev123)

### Docker Commands

```bash
# Start services
yarn docker:up

# Stop services
yarn docker:down

# View logs
yarn docker:logs

# Reset database (removes all data)
yarn docker:reset
```

## 🚀 Manual Setup (Alternative)

### Prerequisites

- Node.js 18+
- pnpm 9.0+
- VS Code

### Installation

1. **Clone and install dependencies:**

   ```bash
   git clone <repository-url>
   cd vscode-sawron
   pnpm install
   ```

2. **Build all packages:**

   ```bash
   pnpm build
   ```

3. **Start development:**

   ```bash
   # Start web app development server
   pnpm --filter web-app dev

   # Or start all packages in development mode
   pnpm dev
   ```

## 📦 Package Details

### VS Code Extension (`packages/vscode-extension`)

**Features:**

- Real-time keystroke tracking
- Operation type classification (TYPE, DELETE, PASTE, etc.)
- Session management
- Excel export functionality
- File and project context tracking

**Commands:**

- `pnpm --filter sawron build` - Build extension
- `pnpm --filter sawron watch` - Watch mode for development

### Web Application (`packages/web-app`)

**Features:**

- Modern Next.js 14+ with App Router
- shadcn/ui components with Tailwind CSS
- Interactive analytics dashboard with Recharts
- AI-powered analysis using Google Gemini
- Excel file upload and parsing
- TypeScript throughout

**Tech Stack:**

- Next.js 14+ with Turbopack
- TypeScript
- Tailwind CSS
- shadcn/ui components
- Recharts for data visualization
- Google Gemini AI integration
- ExcelJS for file parsing

**Commands:**

- `pnpm --filter web-app dev` - Start development server
- `pnpm --filter web-app build` - Build for production
- `pnpm --filter web-app start` - Start production server

### Shared Package (`packages/shared`)

**Features:**

- Common TypeScript types and interfaces
- Utility functions for data processing
- Code quality metrics calculations
- Shared business logic

## 🔧 Development Workflow

### Using pnpm Workspaces

```bash
# Install dependencies for all packages
pnpm install

# Run commands in specific packages
pnpm --filter web-app dev
pnpm --filter sawron build
pnpm --filter @sawron/shared build

# Run commands in all packages
pnpm build
pnpm dev
pnpm lint
pnpm type-check
```

### Building and Testing

```bash
# Build all packages
pnpm build

# Type check all packages
pnpm type-check

# Lint all packages
pnpm lint

# Clean build artifacts
pnpm clean
```

## 🌐 Web Application Usage

1. **Start the web application:**

   ```bash
   pnpm --filter web-app dev
   ```

2. **Open http://localhost:3000**

3. **Upload Excel data:**

   - Export data from the VS Code extension
   - Upload the Excel file to the web app
   - View analytics dashboard

4. **AI Analysis:**
   - Get your Google Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Enter the API key in the AI Analysis tab
   - Get AI-powered insights about your coding patterns

## 🔑 Key Features

### Code Quality Metrics

- **Deletion to Typing Ratio** - Measures code confidence
- **Focus Score** - Based on file switching frequency
- **Productivity Score** - Operations per minute
- **Session Analytics** - Duration and pattern analysis

### AI-Powered Insights

- Coding pattern analysis
- Productivity recommendations
- Code quality suggestions
- Behavioral trend identification

### Data Visualization

- Interactive charts and graphs
- Hourly activity patterns
- File editing statistics
- Daily productivity trends

## 📁 Project Structure

```
vscode-sawron/
├── packages/
│   ├── vscode-extension/     # VS Code extension
│   │   ├── src/
│   │   ├── package.json
│   │   └── tsconfig.json
│   ├── web-app/             # Next.js web application
│   │   ├── src/
│   │   │   ├── app/         # Next.js App Router
│   │   │   └── components/  # React components
│   │   ├── package.json
│   │   └── next.config.js
│   └── shared/              # Shared utilities
│       ├── src/
│       ├── package.json
│       └── tsconfig.json
├── package.json             # Root package.json
├── pnpm-workspace.yaml      # pnpm workspace config
├── turbo.json              # Turborepo configuration
└── README.md
```

## 🛠️ Configuration

### pnpm Workspace

The project uses pnpm workspaces defined in `pnpm-workspace.yaml`:

```yaml
packages:
  - 'packages/*'
```

### Turborepo

Build orchestration is handled by Turborepo with caching and parallel execution.

## 🚀 Deployment

### Web Application

```bash
# Build for production
pnpm --filter web-app build

# Start production server
pnpm --filter web-app start
```

### VS Code Extension

```bash
# Build extension
pnpm --filter sawron build

# Package extension (requires vsce)
cd packages/vscode-extension
vsce package
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `pnpm build && pnpm type-check`
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.
