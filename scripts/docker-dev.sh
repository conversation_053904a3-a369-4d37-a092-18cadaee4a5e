#!/bin/bash

# Sawron Development Environment Setup Script

set -e

echo "🚀 Starting Sawron Development Environment..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "⚠️  Please update the .env file with your actual configuration values."
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p database/init
mkdir -p uploads
mkdir -p logs

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose.dev.yml down

# Remove old volumes (optional - uncomment if you want fresh data)
# echo "🗑️  Removing old volumes..."
# docker-compose -f docker-compose.dev.yml down -v

# Build and start services
echo "🏗️  Building and starting services..."
docker-compose -f docker-compose.dev.yml up -d

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
until docker exec sawron-postgres-dev pg_isready -U sawron_dev -d sawron_dev; do
    echo "Waiting for PostgreSQL..."
    sleep 2
done

echo "✅ PostgreSQL is ready!"

# Wait for Redis to be ready
echo "⏳ Waiting for Redis to be ready..."
until docker exec sawron-redis-dev redis-cli ping; do
    echo "Waiting for Redis..."
    sleep 2
done

echo "✅ Redis is ready!"

echo ""
echo "🎉 Development environment is ready!"
echo ""
echo "📊 Services:"
echo "  - PostgreSQL: localhost:5433"
echo "  - Redis: localhost:6380"
echo "  - pgAdmin: http://localhost:5051"
echo "    - Email: <EMAIL>"
echo "    - Password: dev123"
echo ""
echo "🔧 Next steps:"
echo "  1. Update .env file with your configuration"
echo "  2. Run 'yarn install' to install dependencies"
echo "  3. Run 'yarn workspace web-app dev' to start the web application"
echo ""
echo "📝 Useful commands:"
echo "  - Stop services: docker-compose -f docker-compose.dev.yml down"
echo "  - View logs: docker-compose -f docker-compose.dev.yml logs -f"
echo "  - Reset database: docker-compose -f docker-compose.dev.yml down -v"
echo ""
