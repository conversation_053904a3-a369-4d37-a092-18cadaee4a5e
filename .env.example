# Database Configuration
DATABASE_URL=postgresql://sawron_dev:sawron_dev_password@localhost:5433/sawron_dev
POSTGRES_DB=sawron_dev
POSTGRES_USER=sawron_dev
POSTGRES_PASSWORD=sawron_dev_password

# Redis Configuration
REDIS_URL=redis://localhost:6380

# Authentication (better-auth)
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-super-secret-key-change-this-in-production
BETTER_AUTH_SECRET=your-better-auth-secret-key-change-this

# OAuth Providers (optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# API Keys
GOOGLE_GEMINI_API_KEY=your-gemini-api-key
FIGMA_ACCESS_TOKEN=your-figma-access-token

# SonarQube Configuration
SONARQUBE_URL=http://localhost:9000
SONARQUBE_TOKEN=your-sonarqube-token

# Application Configuration
NODE_ENV=development
PORT=3000
APP_URL=http://localhost:3000

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads

# Logging
LOG_LEVEL=debug
