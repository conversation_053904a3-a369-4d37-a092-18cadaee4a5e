{"name": "sawron-monorepo", "private": true, "description": "Code Quality Analysis Service with VS Code Sawron", "version": "1.0.0", "author": "phil294", "license": "MIT", "packageManager": "yarn@4.9.2", "workspaces": ["packages/*"], "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "type-check": "turbo type-check", "clean": "turbo clean", "install-deps": "yarn install", "docker:dev": "./scripts/docker-dev.sh", "docker:up": "docker-compose -f docker-compose.dev.yml up -d", "docker:down": "docker-compose -f docker-compose.dev.yml down", "docker:logs": "docker-compose -f docker-compose.dev.yml logs -f", "docker:reset": "docker-compose -f docker-compose.dev.yml down -v", "db:migrate": "yarn workspace web-app db:migrate", "db:seed": "yarn workspace web-app db:seed"}, "devDependencies": {"turbo": "^2.5.5", "typescript": "^5.0.0"}}