import * as vscode from 'vscode';
import { appendFile } from 'fs';
import * as path from 'path';
import * as ExcelJS from 'exceljs';

let listener: vscode.Disposable;
let commandListener: vscode.Disposable;

// Analytics data structures
interface KeylogEntry {
  timestamp: Date;
  fileName: string;
  filePath: string;
  fileExtension: string;
  operationType: string;
  operationDetails: string;
  textContent: string;
  characterCount: number;
  sessionId: string;
}

interface AnalyticsData {
  entries: KeylogEntry[];
  operationCounts: Map<string, number>;
  fileCounts: Map<string, number>;
  hourlyActivity: Map<number, number>;
  dailyActivity: Map<string, number>;
  typingSpeed: {
    charactersPerMinute: number;
    wordsPerMinute: number;
    lastCalculated: Date;
  };
  sessionStats: {
    currentSessionId: string;
    sessionStartTime: Date;
    totalSessions: number;
  };
}

// Global analytics storage
let analyticsData: AnalyticsData;

export async function activate() {
  const channel = vscode.window.createOutputChannel('sawron');
  let last_capture = 0;
  let debouncer: NodeJS.Timeout | undefined;
  let buffer = '';
  let lastChangeTime = 0;
  let lastChangeType = '';
  let imeCompositionCount = 0;
  let isLogging = false; // Recursion prevention flag

  // Track document state for deletion capture
  let lastDocumentContent = new Map<string, string>();
  let lastSelectionRange = new Map<string, vscode.Range>();

  // Initialize analytics data
  const sessionId = generateSessionId();
  analyticsData = {
    entries: [],
    operationCounts: new Map(),
    fileCounts: new Map(),
    hourlyActivity: new Map(),
    dailyActivity: new Map(),
    typingSpeed: {
      charactersPerMinute: 0,
      wordsPerMinute: 0,
      lastCalculated: new Date(),
    },
    sessionStats: {
      currentSessionId: sessionId,
      sessionStartTime: new Date(),
      totalSessions: 1,
    },
  };

  // Helper function to generate session ID
  function generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Helper function to get file extension
  function getFileExtension(filePath: string): string {
    const ext = path.extname(filePath);
    return ext || 'no-extension';
  }

  // Helper function to add analytics entry
  function addAnalyticsEntry(
    fileName: string,
    filePath: string,
    operationType: string,
    operationDetails: string,
    textContent: string,
    characterCount: number
  ) {
    const entry: KeylogEntry = {
      timestamp: new Date(),
      fileName,
      filePath,
      fileExtension: getFileExtension(filePath),
      operationType,
      operationDetails,
      textContent,
      characterCount,
      sessionId: analyticsData.sessionStats.currentSessionId,
    };

    analyticsData.entries.push(entry);

    // Update operation counts
    const currentCount = analyticsData.operationCounts.get(operationType) || 0;
    analyticsData.operationCounts.set(operationType, currentCount + 1);

    // Update file counts
    const fileCount = analyticsData.fileCounts.get(filePath) || 0;
    analyticsData.fileCounts.set(filePath, fileCount + 1);

    // Update hourly activity
    const hour = entry.timestamp.getHours();
    const hourlyCount = analyticsData.hourlyActivity.get(hour) || 0;
    analyticsData.hourlyActivity.set(hour, hourlyCount + 1);

    // Update daily activity
    const dateKey = entry.timestamp.toISOString().split('T')[0];
    const dailyCount = analyticsData.dailyActivity.get(dateKey) || 0;
    analyticsData.dailyActivity.set(dateKey, dailyCount + 1);

    // Calculate typing speed for TYPE operations
    if (operationType === 'TYPE') {
      calculateTypingSpeed();
    }
  }

  // Helper function to calculate typing speed
  function calculateTypingSpeed() {
    const now = new Date();
    const timeWindow = 60000; // 1 minute in milliseconds
    const cutoffTime = new Date(now.getTime() - timeWindow);

    const recentTypeEntries = analyticsData.entries.filter(
      (entry) => entry.operationType === 'TYPE' && entry.timestamp >= cutoffTime
    );

    if (recentTypeEntries.length > 0) {
      const totalChars = recentTypeEntries.reduce(
        (sum, entry) => sum + entry.characterCount,
        0
      );
      const totalWords = Math.ceil(totalChars / 5); // Average word length assumption

      analyticsData.typingSpeed.charactersPerMinute = totalChars;
      analyticsData.typingSpeed.wordsPerMinute = totalWords;
      analyticsData.typingSpeed.lastCalculated = now;
    }
  }

  // Selection change listener to track current selection for deletion capture
  const selectionListener = vscode.window.onDidChangeTextEditorSelection(
    (e) => {
      if (e.textEditor && e.textEditor.document) {
        const docUri = e.textEditor.document.uri.toString();
        if (e.selections && e.selections.length > 0) {
          lastSelectionRange.set(docUri, e.selections[0]);
        }
      }
    }
  );

  // 명령어 리스너는 사용하지 않음 - 텍스트 변경 분석으로 대체
  commandListener = {
    dispose: () => {
      selectionListener.dispose();
    },
  };

  // Helper function to format text for logging
  const formatTextForLog = (text: string, maxLength: number = 50): string => {
    if (!text) return '';

    // Replace newlines and tabs with visible characters
    const cleanText = text
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\r')
      .replace(/\t/g, '\\t');

    return cleanText.length > maxLength
      ? `${cleanText.substring(0, maxLength)}...`
      : cleanText;
  };

  // Function to capture deleted text using multiple strategies
  const captureDeletedText = (
    change: vscode.TextDocumentContentChangeEvent,
    document: vscode.TextDocument
  ): string => {
    const docUri = document.uri.toString();
    const prevContent = lastDocumentContent.get(docUri);

    // Strategy 1: Use previous document content to extract deleted text
    if (prevContent) {
      const changeStart = change.rangeOffset;
      const changeEnd = changeStart + change.rangeLength;

      if (changeStart >= 0 && changeEnd <= prevContent.length) {
        return prevContent.substring(changeStart, changeEnd);
      }
    }

    // Strategy 2: Check if we have a previous selection that matches the deletion
    const lastSelection = lastSelectionRange.get(docUri);
    if (lastSelection && !lastSelection.isEmpty && prevContent) {
      const selectionStart = lastSelection.start;
      const selectionEnd = lastSelection.end;

      // Convert positions to offsets using the previous document state
      try {
        const startOffset =
          prevContent
            .split('\n')
            .slice(0, selectionStart.line)
            .reduce((acc, line) => acc + line.length + 1, 0) +
          selectionStart.character;
        const endOffset =
          prevContent
            .split('\n')
            .slice(0, selectionEnd.line)
            .reduce((acc, line) => acc + line.length + 1, 0) +
          selectionEnd.character;

        if (
          Math.abs(startOffset - change.rangeOffset) <= 1 &&
          Math.abs(endOffset - startOffset - change.rangeLength) <= 1
        ) {
          return prevContent.substring(startOffset, endOffset);
        }
      } catch (error) {
        // Ignore position calculation errors
      }
    }

    // Strategy 3: For single character deletions at cursor position
    if (change.rangeLength === 1 && prevContent) {
      const changePos = change.rangeOffset;
      if (changePos >= 0 && changePos < prevContent.length) {
        return prevContent.charAt(changePos);
      }
    }

    // Fallback: Return placeholder indicating we couldn't capture the text
    return `[${change.rangeLength} chars]`;
  };

  // Document state tracking for better deletion capture
  const documentStateTracker = vscode.workspace.onDidOpenTextDocument(
    (document) => {
      const docUri = document.uri.toString();
      lastDocumentContent.set(docUri, document.getText());
    }
  );

  // Track active editor changes to capture document state
  const activeEditorTracker = vscode.window.onDidChangeActiveTextEditor(
    (editor) => {
      if (editor && editor.document) {
        const docUri = editor.document.uri.toString();
        lastDocumentContent.set(docUri, editor.document.getText());
      }
    }
  );

  // Initialize with current active editor
  if (vscode.window.activeTextEditor) {
    const docUri = vscode.window.activeTextEditor.document.uri.toString();
    lastDocumentContent.set(
      docUri,
      vscode.window.activeTextEditor.document.getText()
    );
  }

  listener = vscode.workspace.onDidChangeTextDocument((e) => {
    // Prevent recursion - if we're already logging, skip
    if (isLogging) {
      return;
    }

    const config = vscode.workspace.getConfiguration('sawron');
    const min_delay = config.get<number>('minDelayMs') || 0;
    let max_length = config.get<number>('textMaxLength');
    if (max_length === -1) max_length = undefined;
    const debounce_delay = config.get<number>('debounceMs') || 0;

    // Prevent infinite recursion by filtering out output channels and our own logs
    const fileName = e.document.fileName;
    const uri = e.document.uri.toString();

    if (
      // Filter output channels (various patterns)
      fileName.match(/^extension-output-/) ||
      fileName.match(/^output:/) ||
      uri.includes('extension-output') ||
      uri.includes('output:') ||
      // Filter our specific sawron output
      fileName.includes('sawron') ||
      uri.includes('sawron') ||
      // Filter other common output patterns
      fileName.match(/^untitled:/) ||
      uri.startsWith('output:') ||
      // Rate limiting
      Date.now() - last_capture < min_delay
    ) {
      return;
    }
    last_capture = Date.now();

    // 변경 유형 분석 - CJK IME 처리 개선
    const changes = e.contentChanges;
    let changeType = 'TEXT_INPUT';
    let changeDetails = '';
    const currentTime = Date.now();

    if (changes.length === 1) {
      const change = changes[0];

      // CJK (한중일) 문자 감지
      const isCJKChar = (text: string) =>
        /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\uAC00-\uD7AF]/.test(text);
      const isKoreanJamo = (text: string) => /[\u3131-\u318E]/.test(text); // 한글 자모

      if (change.text === '' && change.rangeLength > 0) {
        // 텍스트가 삭제됨 (Delete, Backspace, Cut) - 실제 삭제된 텍스트 캡처
        changeType = 'DELETE';

        // 삭제된 텍스트 내용 추출 - 새로운 캡처 방법 사용
        const deletedText = captureDeletedText(change, e.document);
        const formattedDeletedText = formatTextForLog(deletedText);

        // Cut 작업 감지를 위한 비동기 클립보드 확인
        setTimeout(() => {
          vscode.env.clipboard.readText().then((clipboardText) => {
            if (clipboardText && clipboardText.length === change.rangeLength) {
              // 클립보드 텍스트 길이가 삭제된 길이와 같으면 Cut 작업으로 간주
              const formattedClipboard = formatTextForLog(clipboardText);
              channel.appendLine(
                `[CUT DETECTED] Cut ${change.rangeLength} character(s): "${formattedClipboard}"`
              );
            }
          });
        }, 50); // 50ms 지연으로 클립보드 업데이트 대기

        changeDetails = `Deleted ${change.rangeLength} character(s): "${formattedDeletedText}"`;
        imeCompositionCount = 0; // IME 카운터 리셋
      } else if (change.text !== '' && change.rangeLength === 0) {
        // 텍스트가 추가됨
        const isMultiChar = change.text.length > 1;
        const hasNewlines = change.text.includes('\n');
        const isLikelyPaste =
          isMultiChar ||
          hasNewlines ||
          (change.text.length > 3 && /[^\w\s]/.test(change.text));

        if (isLikelyPaste) {
          changeType = 'PASTE_OR_BULK';
          changeDetails = `Likely paste/bulk input: "${change.text.substring(
            0,
            50
          )}${change.text.length > 50 ? '...' : ''}"`;
          imeCompositionCount = 0;
        } else {
          changeType = 'TYPE';
          changeDetails = `Typed: "${change.text}"`;

          // 한글 자모 입력시 카운터 증가
          if (isKoreanJamo(change.text)) {
            imeCompositionCount++;
          } else {
            imeCompositionCount = 0;
          }
        }
      } else if (change.text !== '' && change.rangeLength > 0) {
        // 텍스트가 교체됨 - IME 조합 과정 감지
        const isIMEComposition =
          change.rangeLength === 1 &&
          change.text.length === 1 &&
          (isCJKChar(change.text) || isKoreanJamo(change.text)) &&
          currentTime - lastChangeTime < 500; // 500ms 내 연속 변경

        if (isIMEComposition) {
          imeCompositionCount++;
          // IME 조합 과정은 덜 자주 로깅 (매 3번째만)
          if (imeCompositionCount % 3 !== 0) {
            return; // 이 변경사항은 로깅하지 않음
          }
          changeType = 'IME_COMPOSE';
          changeDetails = `IME composition: "${change.text}"`;
        } else {
          changeType = 'REPLACE';

          // 교체된 원본 텍스트 내용 추출
          const replacedText = e.document.getText(change.range);
          const formattedReplaced = formatTextForLog(replacedText, 25);
          const formattedNew = formatTextForLog(change.text, 25);

          changeDetails = `Replaced "${formattedReplaced}" with "${formattedNew}"`;
          imeCompositionCount = 0;
        }
      }
    } else if (changes.length > 1) {
      // 여러 변경사항 (복잡한 편집)
      changeType = 'MULTI_EDIT';
      changeDetails = `Multiple changes: ${changes.length} operations`;
      imeCompositionCount = 0;
    }

    // 상태 업데이트
    lastChangeTime = currentTime;
    lastChangeType = changeType;

    // Add analytics tracking
    const analyticsFileName = path.basename(e.document.fileName);
    const analyticsFilePath = e.document.uri.toString();
    const textContent = changes.map((change) => change.text).join('');
    const characterCount = changes.reduce(
      (sum, change) => sum + Math.max(change.text.length, change.rangeLength),
      0
    );

    addAnalyticsEntry(
      analyticsFileName,
      analyticsFilePath,
      changeType,
      changeDetails,
      textContent,
      characterCount
    );

    buffer += `[${changeType}] ${changeDetails} | ${changes
      .map((change) => change.text)
      .join(' --- ')}`;

    if (debouncer) clearTimeout(debouncer);
    debouncer = setTimeout(() => {
      isLogging = true; // Set flag to prevent recursion

      const line = [
        new Date().toLocaleString(),
        e.document.uri,
        buffer.substring(0, max_length),
      ].join(' - ');
      buffer = '';

      channel.appendLine(line);
      const output_file = config.get<string>('outputFile');
      if (output_file) {
        appendFile(output_file, line + '\n', (err) => {
          if (err) console.error(err);
        });
      }

      isLogging = false; // Reset flag after logging
    }, debounce_delay);

    // Update document state for next deletion capture (after processing current change)
    const docUri = e.document.uri.toString();
    lastDocumentContent.set(docUri, e.document.getText());
  });

  // Register commands for analytics and export
  const exportCommand = vscode.commands.registerCommand(
    'sawron.exportToExcel',
    async () => {
      await exportToExcel();
    }
  );

  const analyticsCommand = vscode.commands.registerCommand(
    'sawron.showAnalytics',
    async () => {
      await showAnalytics();
    }
  );

  const clearDataCommand = vscode.commands.registerCommand(
    'sawron.clearData',
    async () => {
      await clearAnalyticsData();
    }
  );

  // Update command listener to dispose all commands
  commandListener = {
    dispose: () => {
      selectionListener.dispose();
      exportCommand.dispose();
      analyticsCommand.dispose();
      clearDataCommand.dispose();
    },
  };

  // Excel export function
  async function exportToExcel() {
    try {
      const options: vscode.SaveDialogOptions = {
        defaultUri: vscode.Uri.file(
          `sawron-data-${new Date().toISOString().split('T')[0]}.xlsx`
        ),
        filters: {
          'Excel Files': ['xlsx'],
        },
      };

      const fileUri = await vscode.window.showSaveDialog(options);
      if (!fileUri) {
        return; // User cancelled
      }

      await vscode.window.withProgress(
        {
          location: vscode.ProgressLocation.Notification,
          title: 'Exporting sawron data to Excel...',
          cancellable: false,
        },
        async (progress) => {
          progress.report({ increment: 0, message: 'Creating workbook...' });

          const workbook = new ExcelJS.Workbook();
          workbook.creator = 'VS Code sawron Extension';
          workbook.created = new Date();

          // Raw Data Sheet
          progress.report({ increment: 20, message: 'Adding raw data...' });
          await createRawDataSheet(workbook);

          // Summary Statistics Sheet
          progress.report({
            increment: 40,
            message: 'Calculating statistics...',
          });
          await createSummarySheet(workbook);

          // Heatmap Data Sheet
          progress.report({
            increment: 60,
            message: 'Generating heatmap data...',
          });
          await createHeatmapSheet(workbook);

          // Activity Summary Sheet
          progress.report({
            increment: 80,
            message: 'Creating activity summaries...',
          });
          await createActivitySummarySheet(workbook);

          // Save the file
          progress.report({ increment: 90, message: 'Saving file...' });
          await workbook.xlsx.writeFile(fileUri.fsPath);

          progress.report({ increment: 100, message: 'Export complete!' });
        }
      );

      vscode.window
        .showInformationMessage(
          `sawron data exported successfully to ${fileUri.fsPath}`,
          'Open File'
        )
        .then((selection) => {
          if (selection === 'Open File') {
            vscode.env.openExternal(fileUri);
          }
        });
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to export data: ${error}`);
    }
  }

  // Analytics display function
  async function showAnalytics() {
    const stats = generateAnalyticsStats();
    const panel = vscode.window.createWebviewPanel(
      'sawronAnalytics',
      'sawron Analytics',
      vscode.ViewColumn.One,
      { enableScripts: true }
    );

    panel.webview.html = generateAnalyticsHTML(stats);
  }

  // Clear analytics data function
  async function clearAnalyticsData() {
    const result = await vscode.window.showWarningMessage(
      'Are you sure you want to clear all sawron analytics data? This action cannot be undone.',
      'Clear Data',
      'Cancel'
    );

    if (result === 'Clear Data') {
      analyticsData.entries = [];
      analyticsData.operationCounts.clear();
      analyticsData.fileCounts.clear();
      analyticsData.hourlyActivity.clear();
      analyticsData.dailyActivity.clear();
      analyticsData.typingSpeed = {
        charactersPerMinute: 0,
        wordsPerMinute: 0,
        lastCalculated: new Date(),
      };

      vscode.window.showInformationMessage(
        'sawron analytics data cleared successfully.'
      );
    }
  }

  // Helper functions for Excel export
  async function createRawDataSheet(workbook: ExcelJS.Workbook) {
    const worksheet = workbook.addWorksheet('Raw Data');

    // Headers
    worksheet.columns = [
      { header: 'Timestamp', key: 'timestamp', width: 20 },
      { header: 'File Name', key: 'fileName', width: 30 },
      { header: 'File Path', key: 'filePath', width: 50 },
      { header: 'File Extension', key: 'fileExtension', width: 15 },
      { header: 'Operation Type', key: 'operationType', width: 15 },
      { header: 'Operation Details', key: 'operationDetails', width: 40 },
      { header: 'Text Content', key: 'textContent', width: 30 },
      { header: 'Character Count', key: 'characterCount', width: 15 },
      { header: 'Session ID', key: 'sessionId', width: 25 },
    ];

    // Add data
    analyticsData.entries.forEach((entry) => {
      worksheet.addRow({
        timestamp: entry.timestamp,
        fileName: entry.fileName,
        filePath: entry.filePath,
        fileExtension: entry.fileExtension,
        operationType: entry.operationType,
        operationDetails: entry.operationDetails,
        textContent: entry.textContent,
        characterCount: entry.characterCount,
        sessionId: entry.sessionId,
      });
    });

    // Format timestamp column
    worksheet.getColumn('timestamp').numFmt = 'yyyy-mm-dd hh:mm:ss';

    // Style headers
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };
  }

  async function createSummarySheet(workbook: ExcelJS.Workbook) {
    const worksheet = workbook.addWorksheet('Summary Statistics');

    // Calculate summary statistics
    const totalEntries = analyticsData.entries.length;
    const totalCharacters = analyticsData.entries.reduce(
      (sum, entry) => sum + entry.characterCount,
      0
    );
    const sessionDuration =
      new Date().getTime() -
      analyticsData.sessionStats.sessionStartTime.getTime();
    const sessionHours = sessionDuration / (1000 * 60 * 60);

    // Operation type breakdown
    const operationStats = Array.from(
      analyticsData.operationCounts.entries()
    ).map(([type, count]) => ({
      type,
      count,
      percentage: ((count / totalEntries) * 100).toFixed(2),
    }));

    // File extension breakdown
    const extensionCounts = new Map<string, number>();
    analyticsData.entries.forEach((entry) => {
      const count = extensionCounts.get(entry.fileExtension) || 0;
      extensionCounts.set(entry.fileExtension, count + 1);
    });

    // Add summary data
    worksheet.addRow(['Metric', 'Value']);
    worksheet.addRow(['Total Operations', totalEntries]);
    worksheet.addRow(['Total Characters', totalCharacters]);
    worksheet.addRow(['Session Duration (hours)', sessionHours.toFixed(2)]);
    worksheet.addRow([
      'Current CPM',
      analyticsData.typingSpeed.charactersPerMinute,
    ]);
    worksheet.addRow(['Current WPM', analyticsData.typingSpeed.wordsPerMinute]);
    worksheet.addRow([
      'Operations per Hour',
      (totalEntries / Math.max(sessionHours, 0.1)).toFixed(2),
    ]);

    worksheet.addRow([]);
    worksheet.addRow(['Operation Type', 'Count', 'Percentage']);
    operationStats.forEach((stat) => {
      worksheet.addRow([stat.type, stat.count, `${stat.percentage}%`]);
    });

    worksheet.addRow([]);
    worksheet.addRow(['File Extension', 'Count']);
    Array.from(extensionCounts.entries()).forEach(([ext, count]) => {
      worksheet.addRow([ext, count]);
    });

    // Style headers
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(9).font = { bold: true };
    worksheet.getRow(9 + operationStats.length + 2).font = { bold: true };
  }

  async function createHeatmapSheet(workbook: ExcelJS.Workbook) {
    const worksheet = workbook.addWorksheet('Heatmap Data');

    // Hourly activity heatmap
    worksheet.addRow(['Hour', 'Activity Count']);
    for (let hour = 0; hour < 24; hour++) {
      const count = analyticsData.hourlyActivity.get(hour) || 0;
      worksheet.addRow([hour, count]);
    }

    worksheet.addRow([]);
    worksheet.addRow(['Date', 'Activity Count']);
    Array.from(analyticsData.dailyActivity.entries())
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([date, count]) => {
        worksheet.addRow([date, count]);
      });

    worksheet.addRow([]);
    worksheet.addRow(['File Path', 'Edit Count']);
    Array.from(analyticsData.fileCounts.entries())
      .sort(([, a], [, b]) => b - a)
      .forEach(([filePath, count]) => {
        worksheet.addRow([filePath, count]);
      });

    // Style headers
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(26).font = { bold: true };
    worksheet.getRow(26 + analyticsData.dailyActivity.size + 2).font = {
      bold: true,
    };
  }

  async function createActivitySummarySheet(workbook: ExcelJS.Workbook) {
    const worksheet = workbook.addWorksheet('Activity Summary');

    // Group entries by date
    const dailySummary = new Map<
      string,
      {
        date: string;
        totalOperations: number;
        totalCharacters: number;
        operationTypes: Map<string, number>;
        files: Set<string>;
      }
    >();

    analyticsData.entries.forEach((entry) => {
      const dateKey = entry.timestamp.toISOString().split('T')[0];
      if (!dailySummary.has(dateKey)) {
        dailySummary.set(dateKey, {
          date: dateKey,
          totalOperations: 0,
          totalCharacters: 0,
          operationTypes: new Map(),
          files: new Set(),
        });
      }

      const summary = dailySummary.get(dateKey)!;
      summary.totalOperations++;
      summary.totalCharacters += entry.characterCount;
      summary.files.add(entry.filePath);

      const opCount = summary.operationTypes.get(entry.operationType) || 0;
      summary.operationTypes.set(entry.operationType, opCount + 1);
    });

    // Add headers
    worksheet.addRow([
      'Date',
      'Total Operations',
      'Total Characters',
      'Files Edited',
      'Most Common Operation',
    ]);

    // Add daily summaries
    Array.from(dailySummary.values())
      .sort((a, b) => a.date.localeCompare(b.date))
      .forEach((summary) => {
        const mostCommonOp =
          Array.from(summary.operationTypes.entries()).sort(
            ([, a], [, b]) => b - a
          )[0]?.[0] || 'N/A';

        worksheet.addRow([
          summary.date,
          summary.totalOperations,
          summary.totalCharacters,
          summary.files.size,
          mostCommonOp,
        ]);
      });

    // Style headers
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };
  }

  function generateAnalyticsStats() {
    const totalEntries = analyticsData.entries.length;
    const totalCharacters = analyticsData.entries.reduce(
      (sum, entry) => sum + entry.characterCount,
      0
    );
    const sessionDuration =
      new Date().getTime() -
      analyticsData.sessionStats.sessionStartTime.getTime();

    return {
      totalEntries,
      totalCharacters,
      sessionDuration,
      operationCounts: Array.from(analyticsData.operationCounts.entries()),
      fileCounts: Array.from(analyticsData.fileCounts.entries()).slice(0, 10), // Top 10 files
      hourlyActivity: Array.from(analyticsData.hourlyActivity.entries()),
      typingSpeed: analyticsData.typingSpeed,
    };
  }

  function generateAnalyticsHTML(stats: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Sawron Analytics</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .metric { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 5px; }
          .chart { margin: 20px 0; }
          table { border-collapse: collapse; width: 100%; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
        </style>
      </head>
      <body>
        <h1>Sawron Analytics Dashboard</h1>

        <div class="metric">
          <h3>Session Overview</h3>
          <p>Total Operations: ${stats.totalEntries}</p>
          <p>Total Characters: ${stats.totalCharacters}</p>
          <p>Session Duration: ${(
            stats.sessionDuration /
            (1000 * 60 * 60)
          ).toFixed(2)} hours</p>
          <p>Current Typing Speed: ${
            stats.typingSpeed.charactersPerMinute
          } CPM / ${stats.typingSpeed.wordsPerMinute} WPM</p>
        </div>

        <div class="chart">
          <h3>Operation Types</h3>
          <table>
            <tr><th>Operation</th><th>Count</th><th>Percentage</th></tr>
            ${stats.operationCounts
              .map(
                ([type, count]: [string, number]) =>
                  `<tr><td>${type}</td><td>${count}</td><td>${(
                    (count / stats.totalEntries) *
                    100
                  ).toFixed(2)}%</td></tr>`
              )
              .join('')}
          </table>
        </div>

        <div class="chart">
          <h3>Top Files</h3>
          <table>
            <tr><th>File</th><th>Edit Count</th></tr>
            ${stats.fileCounts
              .map(
                ([file, count]: [string, number]) =>
                  `<tr><td>${file}</td><td>${count}</td></tr>`
              )
              .join('')}
          </table>
        </div>

        <div class="chart">
          <h3>Hourly Activity</h3>
          <table>
            <tr><th>Hour</th><th>Activity Count</th></tr>
            ${stats.hourlyActivity
              .map(
                ([hour, count]: [number, number]) =>
                  `<tr><td>${hour}:00</td><td>${count}</td></tr>`
              )
              .join('')}
          </table>
        </div>
      </body>
      </html>
    `;
  }
}

export function deactivate() {
  listener.dispose();
  commandListener.dispose();
}
