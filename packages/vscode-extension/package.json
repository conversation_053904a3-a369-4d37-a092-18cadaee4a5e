{"name": "sawron", "displayName": "sawron", "repository": {"type": "git", "url": "https://github.com/phil294/vscode-keylogger.git"}, "description": "Minimalist extension that logs basic text input events with their timestamp for code quality analysis.", "version": "0.0.2", "author": "phil294", "publisher": "phil294", "license": "MIT", "keywords": ["sawron", "logging", "code quality", "analytics"], "engines": {"vscode": "^1.54.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "sawron.exportToExcel", "title": "Export Sawron Data to Excel", "category": "<PERSON><PERSON>"}, {"command": "sawron.showAnalytics", "title": "Show Sawron Analytics", "category": "<PERSON><PERSON>"}, {"command": "sawron.clearData", "title": "Clear Sawron Data", "category": "<PERSON><PERSON>"}], "configuration": {"title": "sawron", "properties": {"sawron.debounceMs": {"type": "integer", "default": 1000, "description": "Debounce delay in milliseconds. If you set this to something greater than 0, the extension will buffer your input until the delay has elapsed and then log it out combined. The setting sawron.textMaxLength will be applied to the resulting combined string."}, "sawron.minDelayMs": {"type": "integer", "default": -1, "description": "The minimum delay between two events in milliseconds. Faster inputs are simply ignored. Set to <= 0 to log everything."}, "sawron.textMaxLength": {"type": "integer", "default": -1, "description": "Set a maximum length of each captured text. Everything above is cut off. A value of 0 effectively omits the text body in the log. Set to -1 to not cut off anything."}, "sawron.outputFile": {"type": "string", "default": "", "description": "Input a file path to log to, *additionally* to the output channel. Set to an empty string to not write out to any file."}}}}, "scripts": {"vscode:prepublish": "yarn run compile", "compile": "tsc -p ./", "build": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "yarn run compile && yarn run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "type-check": "tsc --noEmit"}, "dependencies": {"@sawron/shared": "workspace:*", "exceljs": "^4.4.0"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/minimatch": "^5.1.2", "@types/mocha": "^10.0.0", "@types/node": "^18.0.0", "@types/vscode": "^1.54.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vscode/vsce": "^3.6.0", "eslint": "^8.0.0", "glob": "^8.1.0", "mocha": "^10.0.0", "typescript": "^5.0.0", "vsce": "^2.15.0", "vscode-test": "^1.6.0"}}