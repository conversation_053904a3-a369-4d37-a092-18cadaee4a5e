import { KeylogEntry, CodeQualityMetrics, AnalyticsData } from './types';

export function calculateCodeQualityMetrics(entries: KeylogEntry[]): CodeQualityMetrics {
  if (entries.length === 0) {
    return {
      deletionToTypingRatio: 0,
      averageSessionDuration: 0,
      fileSwitchingFrequency: 0,
      productivityScore: 0,
      focusScore: 0,
      codeConfidenceScore: 0,
    };
  }

  const typeOperations = entries.filter(e => e.operationType === 'TYPE').length;
  const deleteOperations = entries.filter(e => e.operationType === 'DELETE').length;
  const deletionToTypingRatio = typeOperations > 0 ? deleteOperations / typeOperations : 0;

  // Calculate file switching frequency
  const fileSwitches = entries.reduce((switches, entry, index) => {
    if (index > 0 && entry.filePath !== entries[index - 1].filePath) {
      switches++;
    }
    return switches;
  }, 0);
  const fileSwitchingFrequency = fileSwitches / Math.max(entries.length / 100, 1); // per 100 operations

  // Calculate session durations
  const sessionGroups = new Map<string, KeylogEntry[]>();
  entries.forEach(entry => {
    if (!sessionGroups.has(entry.sessionId)) {
      sessionGroups.set(entry.sessionId, []);
    }
    sessionGroups.get(entry.sessionId)!.push(entry);
  });

  const sessionDurations = Array.from(sessionGroups.values()).map(sessionEntries => {
    if (sessionEntries.length < 2) return 0;
    const sorted = sessionEntries.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    return sorted[sorted.length - 1].timestamp.getTime() - sorted[0].timestamp.getTime();
  });

  const averageSessionDuration = sessionDurations.length > 0 
    ? sessionDurations.reduce((sum, duration) => sum + duration, 0) / sessionDurations.length 
    : 0;

  // Calculate productivity score (operations per minute)
  const totalTime = averageSessionDuration * sessionDurations.length;
  const productivityScore = totalTime > 0 ? (entries.length / (totalTime / 60000)) : 0;

  // Calculate focus score (inverse of file switching frequency)
  const focusScore = Math.max(0, 100 - (fileSwitchingFrequency * 10));

  // Calculate code confidence score (inverse of deletion ratio)
  const codeConfidenceScore = Math.max(0, 100 - (deletionToTypingRatio * 50));

  return {
    deletionToTypingRatio,
    averageSessionDuration: averageSessionDuration / 60000, // Convert to minutes
    fileSwitchingFrequency,
    productivityScore,
    focusScore,
    codeConfidenceScore,
  };
}

export function formatDuration(milliseconds: number): string {
  const hours = Math.floor(milliseconds / (1000 * 60 * 60));
  const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);

  if (hours > 0) {
    return `${hours}h ${minutes}m ${seconds}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  } else {
    return `${seconds}s`;
  }
}

export function getFileExtension(filePath: string): string {
  const match = filePath.match(/\.([^.]+)$/);
  return match ? match[1] : 'unknown';
}

export function groupEntriesByDate(entries: KeylogEntry[]): Map<string, KeylogEntry[]> {
  const groups = new Map<string, KeylogEntry[]>();
  
  entries.forEach(entry => {
    const dateKey = entry.timestamp.toISOString().split('T')[0];
    if (!groups.has(dateKey)) {
      groups.set(dateKey, []);
    }
    groups.get(dateKey)!.push(entry);
  });

  return groups;
}

export function calculateTypingSpeed(entries: KeylogEntry[], timeWindowMs: number = 60000): {
  charactersPerMinute: number;
  wordsPerMinute: number;
} {
  const now = new Date();
  const cutoffTime = new Date(now.getTime() - timeWindowMs);
  
  const recentTypeEntries = entries.filter(
    entry => entry.operationType === 'TYPE' && entry.timestamp >= cutoffTime
  );
  
  if (recentTypeEntries.length === 0) {
    return { charactersPerMinute: 0, wordsPerMinute: 0 };
  }

  const totalChars = recentTypeEntries.reduce((sum, entry) => sum + entry.characterCount, 0);
  const totalWords = Math.ceil(totalChars / 5); // Average word length assumption
  
  return {
    charactersPerMinute: totalChars,
    wordsPerMinute: totalWords,
  };
}
