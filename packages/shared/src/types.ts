// Shared types for keylogger data analysis

export interface KeylogEntry {
  timestamp: Date;
  fileName: string;
  filePath: string;
  fileExtension: string;
  operationType: string;
  operationDetails: string;
  textContent: string;
  characterCount: number;
  sessionId: string;
}

export interface AnalyticsData {
  entries: KeylogEntry[];
  operationCounts: Map<string, number>;
  fileCounts: Map<string, number>;
  hourlyActivity: Map<number, number>;
  dailyActivity: Map<string, number>;
  typingSpeed: {
    charactersPerMinute: number;
    wordsPerMinute: number;
    lastCalculated: Date;
  };
  sessionStats: {
    currentSessionId: string;
    sessionStartTime: Date;
    totalSessions: number;
  };
}

export interface CodeQualityMetrics {
  deletionToTypingRatio: number;
  averageSessionDuration: number;
  fileSwitchingFrequency: number;
  productivityScore: number;
  focusScore: number;
  codeConfidenceScore: number;
}

export interface AIAnalysisResult {
  overallScore: number;
  codeQualityMetrics: CodeQualityMetrics;
  insights: string[];
  recommendations: string[];
  patterns: {
    mostProductiveHours: number[];
    preferredFileTypes: string[];
    editingBehaviorTrends: string[];
  };
}

export interface DashboardData {
  summary: {
    totalOperations: number;
    totalCharacters: number;
    sessionDuration: number;
    averageTypingSpeed: number;
  };
  charts: {
    hourlyActivity: Array<{ hour: number; count: number }>;
    operationTypes: Array<{ type: string; count: number; percentage: number }>;
    topFiles: Array<{ file: string; count: number }>;
    dailyTrends: Array<{ date: string; operations: number; characters: number }>;
  };
  aiAnalysis: AIAnalysisResult;
}

export type OperationType = 
  | 'TYPE' 
  | 'DELETE' 
  | 'PASTE' 
  | 'PASTE_OR_BULK' 
  | 'CUT' 
  | 'REPLACE' 
  | 'IME_COMPOSE' 
  | 'MULTI_EDIT';

export interface ExcelExportOptions {
  dateRange?: {
    start: Date;
    end: Date;
  };
  includeRawData: boolean;
  includeSummary: boolean;
  includeHeatmap: boolean;
  includeActivity: boolean;
}
