'use client';

import { useState, useCallback } from 'react';
import { Upload, FileSpreadsheet, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import * as ExcelJS from 'exceljs';
import { KeylogEntry } from '@sawron/shared';

interface FileUploadProps {
  onDataUploaded: (data: KeylogEntry[]) => void;
}

export function FileUpload({ onDataUploaded }: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const parseExcelFile = useCallback(
    async (file: File): Promise<KeylogEntry[]> => {
      const workbook = new ExcelJS.Workbook();
      const arrayBuffer = await file.arrayBuffer();
      await workbook.xlsx.load(arrayBuffer);

      // Look for the "Raw Data" worksheet
      const worksheet =
        workbook.getWorksheet('Raw Data') || workbook.getWorksheet(1);
      if (!worksheet) {
        throw new Error('No valid worksheet found in the Excel file');
      }

      const entries: KeylogEntry[] = [];
      const headerRow = worksheet.getRow(1);

      // Map column headers to indices
      const columnMap: { [key: string]: number } = {};
      headerRow.eachCell((cell, colNumber) => {
        const header = cell.value?.toString().toLowerCase();
        if (header) {
          if (header.includes('timestamp')) columnMap.timestamp = colNumber;
          if (header.includes('file name')) columnMap.fileName = colNumber;
          if (header.includes('file path')) columnMap.filePath = colNumber;
          if (header.includes('file extension'))
            columnMap.fileExtension = colNumber;
          if (header.includes('operation type'))
            columnMap.operationType = colNumber;
          if (header.includes('operation details'))
            columnMap.operationDetails = colNumber;
          if (header.includes('text content'))
            columnMap.textContent = colNumber;
          if (header.includes('character count'))
            columnMap.characterCount = colNumber;
          if (header.includes('session id')) columnMap.sessionId = colNumber;
        }
      });

      // Parse data rows
      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber === 1) return; // Skip header row

        try {
          const entry: KeylogEntry = {
            timestamp: new Date(
              row.getCell(columnMap.timestamp || 1).value as string
            ),
            fileName:
              row.getCell(columnMap.fileName || 2).value?.toString() || '',
            filePath:
              row.getCell(columnMap.filePath || 3).value?.toString() || '',
            fileExtension:
              row.getCell(columnMap.fileExtension || 4).value?.toString() || '',
            operationType:
              row.getCell(columnMap.operationType || 5).value?.toString() || '',
            operationDetails:
              row.getCell(columnMap.operationDetails || 6).value?.toString() ||
              '',
            textContent:
              row.getCell(columnMap.textContent || 7).value?.toString() || '',
            characterCount:
              Number(row.getCell(columnMap.characterCount || 8).value) || 0,
            sessionId:
              row.getCell(columnMap.sessionId || 9).value?.toString() || '',
          };

          entries.push(entry);
        } catch (error) {
          console.warn(`Error parsing row ${rowNumber}:`, error);
        }
      });

      return entries;
    },
    []
  );

  const handleFileUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validate file type
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
        setError('Please upload a valid Excel file (.xlsx or .xls)');
        return;
      }

      setIsUploading(true);
      setError(null);
      setUploadProgress(0);

      try {
        // Simulate progress updates
        const progressInterval = setInterval(() => {
          setUploadProgress((prev) => Math.min(prev + 10, 90));
        }, 100);

        const entries = await parseExcelFile(file);

        clearInterval(progressInterval);
        setUploadProgress(100);

        if (entries.length === 0) {
          throw new Error('No valid data found in the Excel file');
        }

        // Simulate a brief delay to show completion
        setTimeout(() => {
          onDataUploaded(entries);
          setIsUploading(false);
          setUploadProgress(0);
        }, 500);
      } catch (error) {
        setError(
          error instanceof Error ? error.message : 'Failed to parse Excel file'
        );
        setIsUploading(false);
        setUploadProgress(0);
      }
    },
    [parseExcelFile, onDataUploaded]
  );

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
  }, []);

  const handleDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();
      const file = event.dataTransfer.files[0];
      if (file) {
        const input = document.createElement('input');
        input.type = 'file';
        input.files = event.dataTransfer.files;
        handleFileUpload({
          target: input,
        } as React.ChangeEvent<HTMLInputElement>);
      }
    },
    [handleFileUpload]
  );

  return (
    <div className="space-y-4">
      <div
        className="border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg p-8 text-center hover:border-slate-400 dark:hover:border-slate-500 transition-colors"
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <FileSpreadsheet className="mx-auto h-12 w-12 text-slate-400 mb-4" />
        <div className="space-y-2">
          <Label
            htmlFor="file-upload"
            className="text-lg font-medium cursor-pointer"
          >
            Upload Excel File
          </Label>
          <p className="text-sm text-slate-600 dark:text-slate-400">
            Drag and drop your sawron Excel file here, or click to browse
          </p>
        </div>
        <Input
          id="file-upload"
          type="file"
          accept=".xlsx,.xls"
          onChange={handleFileUpload}
          disabled={isUploading}
          className="hidden"
        />
        <Button
          onClick={() => document.getElementById('file-upload')?.click()}
          disabled={isUploading}
          className="mt-4"
        >
          <Upload className="mr-2 h-4 w-4" />
          {isUploading ? 'Processing...' : 'Choose File'}
        </Button>
      </div>

      {isUploading && (
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Processing Excel file...</span>
            <span>{uploadProgress}%</span>
          </div>
          <Progress value={uploadProgress} className="w-full" />
        </div>
      )}

      {error && (
        <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
          <span className="text-sm text-red-700 dark:text-red-300">
            {error}
          </span>
        </div>
      )}

      <div className="text-xs text-slate-500 dark:text-slate-400">
        <p>Supported formats: Excel (.xlsx, .xls)</p>
        <p>Expected structure: Raw data from VS Code sawron extension export</p>
      </div>
    </div>
  );
}
