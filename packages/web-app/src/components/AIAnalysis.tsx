'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { GoogleGenerativeAI } from '@google/generative-ai';
import {
  AIAnalysisResult,
  calculateCodeQualityMetrics,
  KeylogEntry,
} from '@sawron/shared';
import {
  AlertCircle,
  Brain,
  CheckCircle,
  Lightbulb,
  Loader2,
  Target,
  TrendingUp,
} from 'lucide-react';
import { useState } from 'react';

interface AIAnalysisProps {
  data: KeylogEntry[];
  onAnalysisComplete: (result: AIAnalysisResult | null) => void;
  analysisResult: AIAnalysisResult | null;
}

export function AIAnalysis({
  data,
  onAnalysisComplete,
  analysisResult,
}: AIAnalysisProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [apiKey, setApiKey] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);

  const generateAnalysisPrompt = (entries: KeylogEntry[]) => {
    const metrics = calculateCodeQualityMetrics(entries);

    // Aggregate data for analysis
    const operationCounts = entries.reduce((acc, entry) => {
      acc[entry.operationType] = (acc[entry.operationType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const fileTypes = entries.reduce((acc, entry) => {
      acc[entry.fileExtension] = (acc[entry.fileExtension] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const totalOperations = entries.length;
    const totalCharacters = entries.reduce(
      (sum, entry) => sum + entry.characterCount,
      0
    );

    return `
Analyze the following coding behavior data from a VS Code keylogger and provide insights about code quality and productivity:

**Summary Statistics:**
- Total Operations: ${totalOperations}
- Total Characters: ${totalCharacters}
- Deletion to Typing Ratio: ${metrics.deletionToTypingRatio.toFixed(2)}
- File Switching Frequency: ${metrics.fileSwitchingFrequency.toFixed(2)}
- Average Session Duration: ${metrics.averageSessionDuration.toFixed(2)} minutes

**Operation Breakdown:**
${Object.entries(operationCounts)
  .map(
    ([type, count]) =>
      `- ${type}: ${count} (${((count / totalOperations) * 100).toFixed(1)}%)`
  )
  .join('\n')}

**File Types Worked On:**
${Object.entries(fileTypes)
  .map(([ext, count]) => `- ${ext}: ${count} operations`)
  .join('\n')}

**Quality Metrics:**
- Code Confidence Score: ${metrics.codeConfidenceScore.toFixed(1)}/100
- Focus Score: ${metrics.focusScore.toFixed(1)}/100
- Productivity Score: ${metrics.productivityScore.toFixed(1)}/100

Please provide a comprehensive analysis in the following JSON format:
{
  "overallScore": number (0-100),
  "insights": [
    "string insights about coding patterns",
    "observations about productivity",
    "code quality indicators"
  ],
  "recommendations": [
    "specific actionable recommendations",
    "improvement suggestions",
    "best practices to adopt"
  ],
  "patterns": {
    "mostProductiveHours": [array of hours 0-23],
    "preferredFileTypes": [array of file extensions],
    "editingBehaviorTrends": [array of trend descriptions]
  }
}

Focus on:
1. Code quality indicators based on deletion/typing ratios
2. Productivity patterns and focus levels
3. Editing behavior that might indicate code confidence
4. Suggestions for improvement based on the data
5. Identification of productive vs unproductive patterns

Provide actionable insights that can help improve coding habits and code quality.
`;
  };

  const analyzeWithGemini = async () => {
    if (!apiKey.trim()) {
      setError('Please enter your Google Gemini API key');
      return;
    }

    if (!data || data.length === 0) {
      setError('No data available for analysis');
      return;
    }

    setIsAnalyzing(true);
    setError(null);
    setProgress(0);

    try {
      const genAI = new GoogleGenerativeAI(apiKey);
      const model = genAI.getGenerativeModel({ model: 'gemini-pro' });

      setProgress(25);

      const prompt = generateAnalysisPrompt(data);
      setProgress(50);

      const result = await model.generateContent(prompt);
      setProgress(75);

      const response = await result.response;
      const text = response.text();

      setProgress(90);

      // Try to parse JSON from the response
      let analysisData: AIAnalysisResult;
      try {
        // Extract JSON from the response (in case there's extra text)
        const jsonMatch = text.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          analysisData = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON found in response');
        }
      } catch {
        // Fallback: create a structured response from the text
        analysisData = {
          overallScore: 75, // Default score
          codeQualityMetrics: calculateCodeQualityMetrics(data),
          insights: text
            .split('\n')
            .filter((line) => line.trim().length > 0)
            .slice(0, 5),
          recommendations: [
            'Review the AI analysis for detailed recommendations',
            'Focus on improving code confidence by reducing deletion ratios',
            'Maintain consistent coding sessions for better productivity',
          ],
          patterns: {
            mostProductiveHours: [9, 10, 11, 14, 15, 16],
            preferredFileTypes: Object.keys(
              data.reduce((acc, entry) => {
                acc[entry.fileExtension] = true;
                return acc;
              }, {} as Record<string, boolean>)
            ),
            editingBehaviorTrends: ['Analysis completed with AI insights'],
          },
        };
      }

      setProgress(100);
      onAnalysisComplete(analysisData);

      setTimeout(() => {
        setIsAnalyzing(false);
        setProgress(0);
      }, 500);
    } catch (error) {
      setError(
        error instanceof Error
          ? error.message
          : 'Failed to analyze data with AI'
      );
      setIsAnalyzing(false);
      setProgress(0);
    }
  };

  return (
    <div className="space-y-6">
      {!analysisResult ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AI-Powered Code Quality Analysis
            </CardTitle>
            <CardDescription>
              Get insights about your coding patterns and quality using Google
              Gemini AI
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="api-key">Google Gemini API Key</Label>
              <Input
                id="api-key"
                type="password"
                placeholder="Enter your Gemini API key"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                disabled={isAnalyzing}
              />
              <p className="text-xs text-slate-500">
                Get your API key from{' '}
                <a
                  href="https://makersuite.google.com/app/apikey"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  Google AI Studio
                </a>
              </p>
            </div>

            <Button
              onClick={analyzeWithGemini}
              disabled={isAnalyzing || !apiKey.trim()}
              className="w-full"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Brain className="mr-2 h-4 w-4" />
                  Analyze with AI
                </>
              )}
            </Button>

            {isAnalyzing && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Processing with Google Gemini...</span>
                  <span>{progress}%</span>
                </div>
                <Progress value={progress} className="w-full" />
              </div>
            )}

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {/* Overall Score */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                AI Analysis Complete
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 mb-2">
                  {analysisResult.overallScore}/100
                </div>
                <p className="text-slate-600 mb-4">
                  Overall Code Quality Score
                </p>
                <Badge
                  variant={
                    analysisResult.overallScore >= 80
                      ? 'default'
                      : analysisResult.overallScore >= 60
                      ? 'secondary'
                      : 'destructive'
                  }
                  className="text-sm"
                >
                  {analysisResult.overallScore >= 80
                    ? 'Excellent'
                    : analysisResult.overallScore >= 60
                    ? 'Good'
                    : 'Needs Improvement'}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Insights */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Key Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {analysisResult.insights.map((insight, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                    <span className="text-sm">{insight}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5" />
                Recommendations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {analysisResult.recommendations.map((recommendation, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                    <span className="text-sm">{recommendation}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Patterns */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Identified Patterns
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Most Productive Hours</h4>
                <div className="flex flex-wrap gap-2">
                  {analysisResult.patterns.mostProductiveHours.map((hour) => (
                    <span
                      key={hour}
                      className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-sm"
                    >
                      {hour}:00
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Preferred File Types</h4>
                <div className="flex flex-wrap gap-2">
                  {analysisResult.patterns.preferredFileTypes.map((type) => (
                    <span
                      key={type}
                      className="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded text-sm"
                    >
                      {type}
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Editing Behavior Trends</h4>
                <ul className="space-y-1">
                  {analysisResult.patterns.editingBehaviorTrends.map(
                    (trend, index) => (
                      <li
                        key={index}
                        className="text-sm text-slate-600 dark:text-slate-400"
                      >
                        • {trend}
                      </li>
                    )
                  )}
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Action Button */}
          <Card>
            <CardContent className="pt-6">
              <Button
                onClick={() => {
                  onAnalysisComplete(null);
                  setApiKey('');
                }}
                variant="outline"
                className="w-full"
              >
                Run New Analysis
              </Button>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
