'use client';

import { useMemo } from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
} from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  KeylogEntry,
  calculateCodeQualityMetrics,
  formatDuration,
  groupEntriesByDate,
} from '@sawron/shared';
import { Clock, FileText, Keyboard, TrendingUp } from 'lucide-react';

interface DashboardProps {
  data: KeylogEntry[];
}

const COLORS = [
  '#0088FE',
  '#00C49F',
  '#FFBB28',
  '#FF8042',
  '#8884D8',
  '#82CA9D',
];

export function Dashboard({ data }: DashboardProps) {
  const analytics = useMemo(() => {
    if (!data || data.length === 0) return null;

    // Calculate basic metrics
    const totalOperations = data.length;
    const totalCharacters = data.reduce(
      (sum, entry) => sum + entry.characterCount,
      0
    );

    // Calculate session duration
    const sortedEntries = [...data].sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
    );
    const sessionDuration =
      sortedEntries.length > 1
        ? sortedEntries[sortedEntries.length - 1].timestamp.getTime() -
          sortedEntries[0].timestamp.getTime()
        : 0;

    // Operation type breakdown
    const operationCounts = data.reduce((acc, entry) => {
      acc[entry.operationType] = (acc[entry.operationType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const operationData = Object.entries(operationCounts).map(
      ([type, count]) => ({
        type,
        count,
        percentage: ((count / totalOperations) * 100).toFixed(1),
      })
    );

    // Hourly activity
    const hourlyActivity = data.reduce((acc, entry) => {
      const hour = entry.timestamp.getHours();
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const hourlyData = Array.from({ length: 24 }, (_, hour) => ({
      hour: `${hour}:00`,
      count: hourlyActivity[hour] || 0,
    }));

    // File activity
    const fileCounts = data.reduce((acc, entry) => {
      acc[entry.fileName] = (acc[entry.fileName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topFiles = Object.entries(fileCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([file, count]) => ({ file, count }));

    // Daily trends
    const dailyGroups = groupEntriesByDate(data);
    const dailyData = Array.from(dailyGroups.entries())
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([date, entries]) => ({
        date,
        operations: entries.length,
        characters: entries.reduce(
          (sum, entry) => sum + entry.characterCount,
          0
        ),
      }));

    // Code quality metrics
    const qualityMetrics = calculateCodeQualityMetrics(data);

    return {
      summary: {
        totalOperations,
        totalCharacters,
        sessionDuration,
        averageTypingSpeed:
          sessionDuration > 0 ? totalCharacters / (sessionDuration / 60000) : 0,
      },
      operationData,
      hourlyData,
      topFiles,
      dailyData,
      qualityMetrics,
    };
  }, [data]);

  if (!analytics) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <p className="text-slate-500">No data available for analysis</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Operations
            </CardTitle>
            <Keyboard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics.summary.totalOperations.toLocaleString()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Characters
            </CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics.summary.totalCharacters.toLocaleString()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Session Duration
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDuration(analytics.summary.sessionDuration)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg Typing Speed
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(analytics.summary.averageTypingSpeed)} CPM
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <Tabs defaultValue="operations" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="operations">Operations</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="files">Files</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="operations" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Operation Types</CardTitle>
                <CardDescription>
                  Distribution of different operation types
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={analytics.operationData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ type, percentage }) =>
                        `${type} (${percentage}%)`
                      }
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {analytics.operationData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={COLORS[index % COLORS.length]}
                        />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Operation Counts</CardTitle>
                <CardDescription>
                  Detailed breakdown by operation type
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={analytics.operationData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="type" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Hourly Activity</CardTitle>
              <CardDescription>
                Activity distribution throughout the day
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={analytics.hourlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#00C49F" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="files" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Most Edited Files</CardTitle>
              <CardDescription>Top 10 files by edit count</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>File Name</TableHead>
                    <TableHead className="text-right">Edit Count</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {analytics.topFiles.map((file, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{file.file}</TableCell>
                      <TableCell className="text-right">{file.count}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Daily Activity Trends</CardTitle>
              <CardDescription>
                Operations and characters over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analytics.dailyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line
                    type="monotone"
                    dataKey="operations"
                    stroke="#8884d8"
                    name="Operations"
                  />
                  <Line
                    type="monotone"
                    dataKey="characters"
                    stroke="#82ca9d"
                    name="Characters"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Code Quality Metrics</CardTitle>
              <CardDescription>
                Analysis of coding patterns and behavior
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {analytics.qualityMetrics.codeConfidenceScore.toFixed(1)}
                  </div>
                  <div className="text-sm text-slate-600">Code Confidence</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {analytics.qualityMetrics.focusScore.toFixed(1)}
                  </div>
                  <div className="text-sm text-slate-600">Focus Score</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {analytics.qualityMetrics.productivityScore.toFixed(1)}
                  </div>
                  <div className="text-sm text-slate-600">
                    Productivity Score
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
