'use client';

import { useState } from 'react';
import { Upload, Bar<PERSON><PERSON>3, <PERSON>, FileText } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { FileUpload } from '@/components/FileUpload';
import { Dashboard } from '@/components/Dashboard';
import { AIAnalysis } from '@/components/AIAnalysis';
import { KeylogEntry, AIAnalysisResult } from '@sawron/shared';

export default function Home() {
  const [uploadedData, setUploadedData] = useState<KeylogEntry[] | null>(null);
  const [analysisResult, setAnalysisResult] = useState<AIAnalysisResult | null>(
    null
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-slate-900 dark:text-slate-100 mb-2">
            Code Quality Analysis Service
          </h1>
          <p className="text-lg text-slate-600 dark:text-slate-400">
            AI-powered insights from your VS Code sawron data
          </p>
        </header>

        {!uploadedData ? (
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  Upload Sawron Data
                </CardTitle>
                <CardDescription>
                  Upload your Excel file exported from the VS Code sawron
                  extension to get started with AI-powered code quality
                  analysis.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FileUpload onDataUploaded={setUploadedData} />
              </CardContent>
            </Card>

            <div className="grid md:grid-cols-3 gap-6 mt-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <BarChart3 className="h-5 w-5" />
                    Analytics Dashboard
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Visualize your coding patterns, productivity metrics, and
                    editing behavior with interactive charts and graphs.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Brain className="h-5 w-5" />
                    AI Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Get AI-powered insights about your code quality, typing
                    patterns, and suggestions for improvement using Google
                    Gemini.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <FileText className="h-5 w-5" />
                    Detailed Reports
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Export comprehensive reports with recommendations and
                    actionable insights to improve your coding habits.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          <Tabs defaultValue="dashboard" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
              <TabsTrigger value="analysis">AI Analysis</TabsTrigger>
              <TabsTrigger value="upload">Upload New Data</TabsTrigger>
            </TabsList>

            <TabsContent value="dashboard" className="mt-6">
              <Dashboard data={uploadedData} />
            </TabsContent>

            <TabsContent value="analysis" className="mt-6">
              <AIAnalysis
                data={uploadedData}
                onAnalysisComplete={setAnalysisResult}
                analysisResult={analysisResult}
              />
            </TabsContent>

            <TabsContent value="upload" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Upload New Data</CardTitle>
                  <CardDescription>
                    Replace the current dataset with a new Excel file from your
                    sawron.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <FileUpload onDataUploaded={setUploadedData} />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  );
}
