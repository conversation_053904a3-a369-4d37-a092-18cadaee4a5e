# Development Dockerfile for Next.js Web App
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install dependencies for the entire monorepo
COPY package.json yarn.lock .yarnrc.yml ./
COPY .yarn ./.yarn
COPY packages/web-app/package.json ./packages/web-app/
COPY packages/shared/package.json ./packages/shared/

# Install dependencies
RUN yarn install

# Copy source code
COPY packages/web-app ./packages/web-app
COPY packages/shared ./packages/shared

# Build shared package
WORKDIR /app/packages/shared
RUN yarn build

# Switch to web-app directory
WORKDIR /app/packages/web-app

# Expose port
EXPOSE 3000

# Start development server
CMD ["yarn", "dev"]
