version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: sawron-postgres
    environment:
      POSTGRES_DB: sawron_db
      POSTGRES_USER: sawron_user
      POSTGRES_PASSWORD: sawron_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - sawron-network
    restart: unless-stopped

  # Redis for session storage and caching
  redis:
    image: redis:7-alpine
    container_name: sawron-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - sawron-network
    restart: unless-stopped

  # Web Application
  web-app:
    build:
      context: .
      dockerfile: packages/web-app/Dockerfile
    container_name: sawron-web-app
    environment:
      - NODE_ENV=development
      - DATABASE_URL=******************************************************/sawron_db
      - REDIS_URL=redis://redis:6379
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=your-secret-key-here
    ports:
      - "3000:3000"
    volumes:
      - ./packages/web-app:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - postgres
      - redis
    networks:
      - sawron-network
    restart: unless-stopped
    command: yarn dev

  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: sawron-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - sawron-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  pgadmin_data:

networks:
  sawron-network:
    driver: bridge
