version: '3.8'

services:
  # PostgreSQL Database for Development
  postgres:
    image: postgres:15-alpine
    container_name: sawron-postgres-dev
    environment:
      POSTGRES_DB: sawron_dev
      POSTGRES_USER: sawron_dev
      POSTGRES_PASSWORD: sawron_dev_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5433:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - sawron-dev-network
    restart: unless-stopped

  # Redis for Development
  redis:
    image: redis:7-alpine
    container_name: sawron-redis-dev
    ports:
      - "6380:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - sawron-dev-network
    restart: unless-stopped

  # pgAdmin for Development
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: sawron-pgadmin-dev
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: dev123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5051:80"
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - sawron-dev-network
    restart: unless-stopped

volumes:
  postgres_dev_data:
  redis_dev_data:
  pgadmin_dev_data:

networks:
  sawron-dev-network:
    driver: bridge
